package com.pharmeasy.service

import com.pharmeasy.constants.DefaultConst
import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.success
import com.pharmeasy.proxy.UserProxy
import com.pharmeasy.repo.*
import com.pharmeasy.type.*
import com.pharmeasy.util.ChequeFileUtils
import com.pharmeasy.util.UUIDUtil
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.springframework.http.HttpStatus
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import kotlin.collections.get
import kotlin.math.round

@Service
class ChequeHandlingService(@Value("\${app.dnToRetailerCBHsn}") val dnToRetailerCBHsn: String,
                            @Value("\${app.dnToRetailerCBTax}") val dnToRetailerCBTax: Int,
                            private var chequeHandlingRepo: ChequeHandlingRepo,
                            private var chequeHandlingLog: ChequeHandleLogRepo,
                            private var adjustmentService: AdjustmentService,
                            private var documentMasterService: DocumentMasterService,
                            private var checkerService: CheckerService,
                            private var companyService: CompanyService,
                            private var s3FileUtilityService : S3FileUtilityService,
                            private var userProxy: UserProxy,
                            private var retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService,
                            private var invoiceSettlementMappingService: InvoiceSettlementMappingService
    ) {

    companion object {
        private val log = LoggerFactory.getLogger(ChequeHandlingService::class.java)
        private val terminalChequeStatuses = listOf(ChequeHandleType.AFTER_CLEAR_BOUNCE, ChequeHandleType.BOUNCE, ChequeHandleType.CANCELLED)
    }
    @Autowired
    private lateinit var advancePaymentService: AdvancePaymentService
    @Autowired
    private lateinit var receiptService: ReceiptService
    @Autowired
    private lateinit var retailerDebitNoteService: RetailerDebitNoteService
    @Autowired
    private lateinit var receiptRepo: ReceiptRepo
    @Autowired
    private lateinit var settlementService: SettlementService
    @Autowired
    private lateinit var invoiceService: InvoiceService
    @Autowired
    private lateinit var settlementRepo: SettlementRepo

    private val validTransitions = mapOf(
        ChequeHandleType.RECEIVED to listOf(ChequeHandleType.DEPOSITED, ChequeHandleType.CANCELLED),
        ChequeHandleType.DEPOSITED to listOf(ChequeHandleType.CLEARED, ChequeHandleType.BOUNCE),
        ChequeHandleType.CLEARED to listOf(ChequeHandleType.AFTER_CLEAR_BOUNCE)
    )
    fun addChequeHandleEntry(user: String, chequeObj: CreateChequeHandleDto) {
        val checkers = checkerService.findCheckers(chequeObj.tenant,null,null)
        val chequeHandle = ChequeHandle(
                tenant = chequeObj.tenant,
                chequeNum = chequeObj.chequeNumber,
                settlementId = chequeObj.settlementId,
                assignTo = checkers[0]?.userId,
                status = ChequeHandleType.RECEIVED,
                inApprove = false,
                settled = false,
                advancePaymentId = chequeObj.advanceId,
                receiptId = chequeObj.receiptId
        )
        val chequeHandleSave = chequeHandlingRepo.save(chequeHandle)
        chequeHandlingLog.save(ChequeHandleLog(null, chequeHandleSave.id!!, ChequeHandleType.RECEIVED, LocalDateTime.now(), user))
    }

    @Transactional
    fun updateChequeStatus(chequeUpdate: ChequeUpdateDto, tenant: String, email:String, ds: String? = null): ChequeHandle? {

        var chequeHandle = chequeHandlingRepo.findByIdOrNull(chequeUpdate.id)?:
        throw RequestException("Cheque not found for id ${chequeUpdate.id} !")
        if (chequeHandle.status in terminalChequeStatuses) {
            throw RequestException("cheque status is already in final state !")
        }

        if (!validateChequeStatusUpdate(chequeHandle.status!!, chequeUpdate.status)) {
            throw RequestException("Invalid status transition from ${chequeHandle.status} to ${chequeUpdate.status}")
        }

        if (chequeUpdate.status == ChequeHandleType.CLEARED) {
            val res = chequeHandlingRepo.getChequeHandlingDataById(chequeHandle.id)

            val chequeLogs = chequeHandlingLog.getByDraftIdAndStatus(chequeHandle.id!!, ChequeHandleType.DEPOSITED)

            val depositDate = if(chequeLogs.isNotEmpty()) chequeLogs.last().createdOn.toLocalDate() else res.chequeDate
            val clearedDate = chequeUpdate.updatedOn?.toLocalDate()

            if (clearedDate!!.isBefore(depositDate) || clearedDate.isAfter(LocalDate.now())) {
                throw RequestException("Cheque `Date of Cleared` must be between the Cheque date and the current date")
            }
            if (res.status!!.name != ChequeHandleType.DEPOSITED.name){
                throw RequestException("cheque status must be DEPOSITED to update to CLEARED")
            }
        }
        if (chequeUpdate.status.name == ChequeHandleType.BOUNCE.name || chequeUpdate.status.name == ChequeHandleType.AFTER_CLEAR_BOUNCE.name) {
            if(chequeUpdate.status.name == ChequeHandleType.AFTER_CLEAR_BOUNCE.name){
                chequeHandle.afterClearanceBounce = true
            }
            val checker = checkerCheck(chequeUpdate.updatedBy,tenant,ds)
            chequeUpdate.status = ChequeHandleType.PENDING_APPROVAL
            chequeHandle = updateCheckerDetail(chequeUpdate, tenant,checker,ds, chequeHandle.afterClearanceBounce)

        } else {

            if (chequeUpdate.status.name == ChequeHandleType.CANCELLED.name && (chequeHandle.status!!.name != ChequeHandleType.RECEIVED.name))
                throw RequestException("cheque status not in RECEIVED state cant be cancelled !")
            chequeHandle.updatedOn = chequeUpdate.updatedOn
            chequeHandle.status = chequeUpdate.status
            chequeHandle.reason = chequeUpdate.reason
            chequeHandle.updatedBy = chequeUpdate.updatedBy
        }
        if(chequeUpdate.status.name != ChequeHandleType.RECEIVED.name){
           chequeHandlingLog.save(ChequeHandleLog(null, chequeUpdate.id, chequeUpdate.status, chequeUpdate.updatedOn!!, chequeUpdate.updatedBy))
        }

        if( chequeHandle.settled == false && chequeUpdate.status.name != ChequeHandleType.CANCELLED.name && chequeHandle.settlementId != null){
            val settlement =
                settlementRepo.findByIdOrNull(chequeHandle.settlementId)?:throw
                RequestException("No data found for settlement id ${chequeHandle.settlementId!!} !")
            settlement.invoices = invoiceSettlementMappingService.getInvoicesForSettlement(settlement.id).toMutableList()
            settlement.retailerDebitNotes = retailerDebitNoteService.getRetailerDebitNoteBySettlementId(settlement.id).toMutableList()
            settlement.chargeInvoice = mutableListOf()

            val receipt = receiptService.checkExistingReceipt(settlement)
            if(receipt != null && receipt.source == AdvancePaymentSource.RIO_COLLECTIONS) {
                //for RIO_COLLECTIONS ledger for non-advance ledger will be impacted once status is deposited
                log.info("Cheque status updated for Rio Collection Cheque. " +
                        "Ledger entry will create once cheque status is updated to cleared in case of advance")
            }else{
                settlementService.updateVendorLedger(settlement.createdBy!!, settlement, true)
            }
            chequeHandle.settled = true

        }else if(chequeUpdate.status.name == ChequeHandleType.CANCELLED.name){
            if(chequeHandle.settlementId != null)
                reverseSettlementAndInvoice(chequeUpdate.updatedBy,chequeHandle.id!!)
            else {
                advancePaymentService.changeAdvancePaymentStatus(chequeHandle.advancePaymentId?:0,Status.CANCELLED)
            }
        }

        if(chequeUpdate.status == ChequeHandleType.CLEARED && chequeHandle.advancePaymentId != null){
            advancePaymentService.changeAdvancePaymentStatus(chequeHandle.advancePaymentId?:0,Status.APPROVED)
            chequeHandle.settled = true
        }else if(chequeUpdate.status.name == ChequeHandleType.DEPOSITED.name && chequeHandle.advancePaymentId == null){
            val settlement =
                settlementRepo.findByIdOrNull(chequeHandle.settlementId)?:throw
                RequestException("No data found for settlement id ${chequeHandle.settlementId!!} !")
            val receipt = receiptService.checkExistingReceipt(settlement)
            if(receipt != null && receipt.source == AdvancePaymentSource.RIO_COLLECTIONS && (receipt.advanceAmount == 0.00 || receipt.advanceAmount == null)) {
                receiptService.createLedgerFromReceipt(receipt, settlement)
            }
            chequeHandle.settled = true

        }else if(chequeUpdate.status.name == ChequeHandleType.CLEARED.name && chequeHandle.advancePaymentId == null){
            val settlement =
                settlementRepo.findByIdOrNull(chequeHandle.settlementId)?:throw
                RequestException("No data found for settlement id ${chequeHandle.settlementId!!} !")
            val receipt = receiptService.checkExistingReceipt(settlement)
            if((receipt != null) && (receipt.advanceAmount != null) && (receipt.source == AdvancePaymentSource.RIO_COLLECTIONS) && ((receipt.advanceAmount?:0.00) > 0.00)) {
                receiptService.createAdvancePaymentFromReceipt(receipt, settlement, chequeUpdate.updatedBy, email, ds)
                receiptService.createLedgerFromReceipt(receipt,settlement, settlement.paidAmount+receipt.advanceAmount!!)
            }
            chequeHandle.settled = true

        }
        if(validateChequeStatus(chequeUpdate.id, chequeUpdate.status)){
            return chequeHandle
        }
        chequeHandlingRepo.save(chequeHandle)
        return chequeHandle

    }

    fun updateCheckerDetail(chequeUpdate: ChequeUpdateDto, tenant: String,checker:CheckerDetails?,ds: String? = null,afterClearanceBounce:Boolean = false): ChequeHandle {
        log.debug("inside updateCheckerDetail cheque handling : updatedBy = ${chequeUpdate.updatedBy} tenant $tenant")
        val checkers = checkerService.findCheckers(tenant,null,ds)

        if (checkers.isNotEmpty()) {
            val checker = checkers[0]
            log.debug("checker :{} ", checker)
            if (checker?.userId.equals(chequeUpdate.updatedBy)) throw RequestException("Users having Checker rights, cannot update cheque status. Please request Maker!")

            val chequeHandle = chequeHandlingRepo.getOne(chequeUpdate.id)
            log.debug("chequeHandle :{}", chequeHandle)
            chequeHandle.assignTo = checker?.userId
            chequeHandle.inApprove = true
            chequeHandle.status = chequeUpdate.status
            chequeHandle.reason = chequeUpdate.reason
            chequeHandle.updatedOn = chequeUpdate.updatedOn
            chequeHandle.charge = chequeUpdate.charge
            chequeHandle.chargeAmt = round(chequeUpdate.chargeAmt?:0.00)
            chequeHandle.bounceDate = chequeUpdate.bounceDate
            chequeHandle.afterClearanceBounce = afterClearanceBounce
            return chequeHandlingRepo.save(chequeHandle)
        } else {
            throw RequestException("No checker found for this company!")
        }
    }

    @Transactional
    fun checkerStatusChange(userId: String, change: Boolean, chequeHandleId: Long, tenant: String, ds: String? = null,chequeObj:ChequeHandle?=null): Result {
        if(validateChequeStatus(chequeHandleId, ChequeHandleType.BOUNCE)){
            return Result(200, "Request already approved")
        }
        val checkers = checkerService.findCheckers(ds?:tenant)


        if (checkers.isNotEmpty()) {
            val checker = checkerService.compareChecker(checkers,userId)?:throw RequestException("Users not having checker rights, cannot update cheque status. Please request Checker!")

            val chequeHandle = chequeHandlingRepo.findByIdOrNull(chequeHandleId)
                ?: throw RequestException("id $chequeHandleId is not present in our DB! ")

            if (
                chequeHandle.status == ChequeHandleType.PENDING_APPROVAL &&
                chequeHandle.afterClearanceBounce &&
                java.time.temporal.ChronoUnit.DAYS.between(chequeHandle.updatedOn, now()) > 45
            ) {
                chequeHandle.status = ChequeHandleType.CLEARED
                chequeHandle.inApprove = false
                chequeHandlingRepo.save(chequeHandle)
                chequeHandlingLog.save(ChequeHandleLog(null, chequeHandleId, chequeHandle.status?:ChequeHandleType.BOUNCE, LocalDateTime.now(), userId))
                //status 400 to show the message in UI. This is not HTTP status code
                return Result(400, "Cheque moved back to cleared state as approval was done after 45 days")
            }

            if(chequeHandle.createdBy == userId){
                throw RequestException("Not Approved as created by ${checker.userName}")
            }

            if (change) {
                //if approved by checker
                updateBouncedStatus(userId, true, chequeHandleId, ds ?: tenant, checker, chequeObj)
            }
            else {
                //if rejected by checker
                chequeHandle.status = if(chequeHandle.afterClearanceBounce){
                    ChequeHandleType.CLEARED
                }else {
                    ChequeHandleType.DEPOSITED
                }
                chequeHandle.inApprove = false
                chequeHandlingRepo.save(chequeHandle)
                chequeHandlingLog.save(ChequeHandleLog(null, chequeHandleId, chequeHandle.status?:ChequeHandleType.BOUNCE, LocalDateTime.now(), userId))

            }
        } else {
            throw RequestException("No checker found for this company!")
        }
        return Result(200, "SUCCESS")
    }

    @Transactional
    fun updateBouncedStatus(userId: String, change: Boolean, chequeHandleId: Long, tenant: String, checker: CheckerDetails,chequeObj:ChequeHandle?=null) {
        if (userId != checker.userId) throw RequestException("Users not having checker rights, cannot update cheque status. Please request Checker!")

        val chequeHandle = chequeObj ?: (chequeHandlingRepo.findByIdOrNull(chequeHandleId) ?: throw RequestException("id $chequeHandleId is not present in our DB! "))


        val company = companyService.getCompanyByTenant(chequeHandle.tenant)?:throw RequestException("Company not found for tenant ${chequeHandle.tenant} !")

        val pdi:Long
        if(chequeHandle.settlementId != null) {
            val settlement = settlementRepo.findByIdOrNull(chequeHandle.settlementId)
                ?: throw RequestException("settlement id $chequeHandleId is not present in our DB! ")
            if(settlement.reversed == true) throw RequestException("Settlement number ${settlement.settlementNumber} against cheque number ${chequeHandle.chequeNum} is already reversed!")

            val invoiceObj = invoiceSettlementMappingService.getInvoicesForSettlement(settlement.id)
            val retailerDnObject = retailerDebitNoteService.getRetailerDebitNoteBySettlementId(settlement.id)

            if(invoiceObj.isNotEmpty()) {
                settlement.invoices = invoiceObj.toMutableList()
                val settlementInvoiceObj = invoiceSettlementMappingService.getSettlementInvoiceBySettlementId(settlement.id)
                val invoiceAmtMap = settlementInvoiceObj.associateBy { it.id.invoiceId }
                invoiceService.updateInvoiceAmtForBounceCheque(invoiceObj, invoiceAmtMap)
            }else if(retailerDnObject.isNotEmpty()){
                settlement.retailerDebitNotes = retailerDnObject.toMutableList()
                val settlementDebitNoteObj = retailerDebitNoteSettlementMappingService.getRetailerDebitNoteSettlementBySettlementId(settlement.id)
                val retailerDnAmtMap = settlementDebitNoteObj.associateBy { it.retailerDebitNoteId.id!! }
                retailerDebitNoteService.updateRetailerDebitNoteAmtForBounceCheque(retailerDnObject, retailerDnAmtMap)
            }

            val receipt = receiptRepo.getReceiptBySettlementId(settlement.id)
            var rioCollectionConditionWithAdvance = false
            if(receipt?.isNotEmpty() == true){
                rioCollectionConditionWithAdvance = chequeHandle.status == ChequeHandleType.PENDING_APPROVAL && receipt[0].source == AdvancePaymentSource.RIO_COLLECTIONS && (receipt[0].advanceAmount?:0.00) > 0.00
            }
            if(chequeHandle.settled == true && !rioCollectionConditionWithAdvance) {
                createLedgerForBounce(userId, checker, settlement, chequeHandle, change, invoiceObj, company)
            }
            if(chequeHandle.settled == true && rioCollectionConditionWithAdvance){
                var receiptAmount = 0.0
                receipt?.forEach {
                    val advancePaymentObj = advancePaymentService.changeAdvancePaymentStatus(it.advanceId?:0,Status.CANCELLED)
                    advancePaymentService.reverseAdvancePaymentSettlements(advancePaymentObj,userId)
                    receiptAmount += it.amount?:0.0
                }
                val settlementCopy = settlement
                settlementCopy.paidAmount = receiptAmount
                createLedgerForBounce(userId, checker, settlementCopy, chequeHandle, change, invoiceObj, company)
            }
            settlement.isBounced = true
            settlementRepo.save(settlement)
            pdi=settlement.partnerDetailId!!
        }else{
            val advancePaymentObj = advancePaymentService.changeAdvancePaymentStatus(chequeHandle.advancePaymentId?:0,Status.CANCELLED)
            if(chequeHandle.afterClearanceBounce) {
                createLedgerForBounce(userId, checker, null, chequeHandle, change, null, company,advancePaymentObj)
                advancePaymentService.reverseAdvancePaymentSettlements(advancePaymentObj,userId)
            }

            pdi=advancePaymentObj.partnerDetailId!!
        }
            chequeHandle.status = if(!chequeHandle.afterClearanceBounce) ChequeHandleType.BOUNCE else ChequeHandleType.AFTER_CLEAR_BOUNCE
            chequeHandle.inApprove = false
            chequeHandle.settled = false
            val chequeHandleObj = chequeHandlingRepo.save(chequeHandle)
            chequeHandlingLog.save(ChequeHandleLog(null, chequeHandleId, chequeHandle.status?:ChequeHandleType.BOUNCE, LocalDateTime.now(), userId))

        if(chequeHandle.charge == true) {
            val taxAmount = BigDecimal((chequeHandleObj.chargeAmt ?: 0.0) * dnToRetailerCBTax.toDouble())
                .divide(BigDecimal(100), 2, RoundingMode.HALF_UP)
                .toDouble()
            val amount  = chequeHandleObj.chargeAmt?.plus(taxAmount)
            val retailerDebitNoteDto = RetailerDebitNoteDto(
                    chequeHandleObj.chequeNum,
                    DnType.BOUNCE_CHARGE,
                    amount!!, 0.0, chequeHandleObj.chargeAmt, taxAmount, dnToRetailerCBTax.toDouble(), pdi,
                     chequeHandleId, UseCaseType.BOUNCE, false, dnToRetailerCBHsn, "",false, UUIDUtil.generateUuid()
            )
            retailerDebitNoteService.createRetailerDebitNote(checker.userName,
                retailerDebitNoteDto, tenant, chequeHandle)
        }


    }

    private fun createLedgerForBounce(
        userId: String,
        checker: CheckerDetails,
        settlement: Settlement?,
        chequeHandle: ChequeHandle,
        change: Boolean,
        invoiceObj: List<BkInvoice>?,
        company: Company,
        advancePayment: AdvancePayment? = null
    ) {
        val adjustmentObj = AdjustmentEntry(
            0,
            LocalDateTime.now(),
            LocalDateTime.now(),
            userId,
            userId,
            checker.userName,
            checker.userId,
            userId,
            LocalDate.now(),
            settlement?.supplierName?:advancePayment?.vendorName,
            LedgerEntryType.DEBIT,
            null,
            DocumentType.CHEQUE_BOUNCED,
            settlement?.type?:advancePayment?.type?:PartnerType.CUSTOMER,
            Status.APPROVED,
            "${chequeHandle.chequeNum} - Bounced settlement",
            settlement?.paidAmount?.toBigDecimal() ?: (advancePayment?.amount?: BigDecimal.ZERO),
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            settlement?.partnerId ?: (advancePayment?.partnerId)?:0,
            settlement?.partnerDetailId ?: (advancePayment?.partnerDetailId)?:0,
            settlement?.tenant ?: (advancePayment?.tenant)?:"",
            checker.companyId,
            "",
            if (!change){
                if(invoiceObj!= null)
                invoiceObj[0].client
                else
                    advancePayment?.client?:InvoiceType.VENDOR
            } else InvoiceType.VENDOR
        )

        adjustmentObj.documentId = documentMasterService.getDocumentNumber(
            adjustmentObj.updatedBy!!,
            company.companyCode,
            DocumentType.CHEQUE_BOUNCED
        )

        adjustmentService.addAdjustmentToLedger(
            adjustmentObj,
            settlement?.settlementNumber?:advancePayment?.documentId?:"-",
            if (!change) {
                if(invoiceObj!= null)
                    invoiceObj[0].invoiceNum
                else
                    advancePayment?.documentId?:"-"
            } else ""
        )
        adjustmentService.updateVendorBalance(adjustmentObj)
    }

    fun getAggregatedChequeData(tenant: String, ds: String? = null, partnerDetailId: Long?): AggregatedChequeHandleDto {
        val tenants=companyService.findTenants(ds?:tenant)
        return chequeHandlingRepo.getAggregatedChequeDataV2(tenants, partnerDetailId)
    }

    fun getChequeStatusTooltip(cheques: List<ChequeHandleDto>): List<ChequeStatusToolTip> {

        val chequeTooltips = mutableListOf<ChequeStatusToolTip>()

        if (cheques.isEmpty()) {
            return emptyList()
        }


        val chequesMap = chequeHandlingLog.getByDraftIds(cheques.map { it.id }).groupBy { it.draftId }

        cheques.forEach { cheque ->

            val statusLogsMap = chequesMap[cheque.id]?.associateBy { it.status } ?: emptyMap()

            log.debug("status logs Map :{}", statusLogsMap)

            val chequeStatusToolTip = ChequeStatusToolTip(cheque, null, null, null, null)


            if (statusLogsMap.containsKey(ChequeHandleType.RECEIVED)) {
                chequeStatusToolTip.recievedOn = statusLogsMap[ChequeHandleType.RECEIVED]!!.createdOn.toLocalDate()
            }

            if (statusLogsMap.containsKey(ChequeHandleType.DEPOSITED)) {
                chequeStatusToolTip.depositedOn = statusLogsMap[ChequeHandleType.DEPOSITED]!!.createdOn.toLocalDate()
            }

            if (statusLogsMap.containsKey(ChequeHandleType.CLEARED)) {
                chequeStatusToolTip.clearedOn = statusLogsMap[ChequeHandleType.CLEARED]!!.createdOn.toLocalDate()
            }

            if (statusLogsMap.containsKey(ChequeHandleType.BOUNCE)) {
                chequeStatusToolTip.bouncedOn = cheque.bounceDate?.toLocalDate()?: statusLogsMap[ChequeHandleType.BOUNCE]!!.createdOn.toLocalDate()
            }

            if (statusLogsMap.containsKey(ChequeHandleType.CANCELLED)) {
                chequeStatusToolTip.cancelledOn = statusLogsMap[ChequeHandleType.CANCELLED]!!.createdOn.toLocalDate()
            }

            if (statusLogsMap.containsKey(ChequeHandleType.AFTER_CLEAR_BOUNCE)) {
                chequeStatusToolTip.afterClearanceBouncedOn = cheque.bounceDate?.toLocalDate()?: statusLogsMap[ChequeHandleType.AFTER_CLEAR_BOUNCE]!!.createdOn.toLocalDate()
            }

            log.debug("chequeStatusToolTip :{}",chequeStatusToolTip)
            chequeTooltips.add(chequeStatusToolTip)
        }
        log.debug("chequeTooltips:{}", chequeTooltips)
        return chequeTooltips
    }

    fun checkerCheck(userId: String,tenant: String,ds: String? = null) : CheckerDetails? {
        val checkers = checkerService.findCheckers(tenant,null,ds)

        if (checkers.isNotEmpty()) {

            checkers.forEach {
                val checker = it
                if (checker?.userId.equals(userId)) throw RequestException("Users having Checker rights, cannot update cheque status. Please request Maker!")
                else
                    return checker
            }

        }
        return null
    }

    @Transactional
    fun updateChequeChecker(chequeId: Long, checkerId: Long, tenant: String, userId: String, ds: String? = null): Result{


        val checker = checkerService.findCheckers(tenant,checkerId,ds)

        if(checker.isEmpty())
            throw RequestException("No active checker found with id  $checkerId , for logged in company!")

        if(checker.size>1)
            throw RequestException("More than 1 checker found $checkerId , for logged in company!")

        val chequeObj = chequeHandlingRepo.get(chequeId) ?: throw RequestException("No cheque entry found with id $chequeId !")

        if(chequeObj.status != ChequeHandleType.PENDING_APPROVAL) throw RequestException("cheque with id $chequeId is not in PENDING_APPROVAL state!")

        val companyId = companyService.getCompanyTenantMappingObject(ds?:tenant)?.companyId
            ?: throw RequestException("Company not found for the given tenant")

        val checkerUserIdList = checkerService.getCheckerByCompanyId(companyId).mapNotNull { it?.userId }.toMutableList()

        if(userId != chequeObj.createdBy && userId !in checkerUserIdList){
            throw RequestException("not a valid user cannot update")
        }

        chequeObj.assignTo = checker[0]?.userId
        chequeObj.updatedBy = userId

        chequeHandlingRepo.save(chequeObj)

        return success
    }

    fun getTotalBounceNumber(tenant: String,partnerId:Long): Long{
        val tenants=companyService.findTenants(tenant)

        return chequeHandlingRepo.getPastBounceNumberV2(tenants,partnerId)


    }

    fun getChequeUrl(tenant: String, ds: String? = null, fromDate: LocalDate,toDate: LocalDate,status: ChequeHandleType?) : CreateResultData {
        val tenants = companyService.findTenants(ds?:tenant)

        if (tenants.isEmpty())  throw RequestException("tenant $tenant not found!")
        val res = mutableListOf<ChequeHandleDto>()
        var currentPage = 0
        do {
            val pagination = PageRequest.of(currentPage, 500)
            val page = chequeHandlingRepo.getChequeHandlingDataWithoutIdsV2(
                null,status ?: ChequeHandleType.RECEIVED, null, fromDate, toDate, tenants, null, pagination
            )
            res.addAll(page.content)
            currentPage++
        } while (res.size < page.totalElements && page.content.isNotEmpty())
        if (res.isEmpty()) throw RequestException("Data is not available")

        // Create a separate function for CSV printing and associated activities
        val uploaded = printChequeDataToCSV(res)

        return CreateResultData(HttpStatus.OK.value(), HttpStatus.OK.reasonPhrase, uploaded)
    }

    private fun printChequeDataToCSV(data: List<ChequeHandleDto>): String {
        val prefix = "ChequeHandleList-"

        val csvPrinter: CSVPrinter
        val bytes = ByteArrayOutputStream()

        csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT
            .withHeader(ChequeFileUtils.chequeFileCols[0], ChequeFileUtils.chequeFileCols[1], ChequeFileUtils.chequeFileCols[2], ChequeFileUtils.chequeFileCols[3], ChequeFileUtils.chequeFileCols[4], ChequeFileUtils.chequeFileCols[5], ChequeFileUtils.chequeFileCols[7], ChequeFileUtils.chequeFileCols[6]))
        data.forEach {
            csvPrinter.printRecord(it.id,it.chequeNum, it.chequeDate, it.amt,
                it.partnarDetailId, it.customerName,it.bank, it.status)
        }
        csvPrinter.flush()

        val uploaded = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", prefix) ?:
        throw RequestException("s3 url failed to generated!")

        return uploaded
    }

    fun reverseSettlementAndInvoice(userId: String, chequeHandleId: Long) {


        val chequeHandle = chequeHandlingRepo.findByIdOrNull(chequeHandleId)
            ?: throw RequestException("id $chequeHandleId is not present in our DB! ")

        val settlement = settlementRepo.findByIdOrNull(chequeHandle.settlementId)
            ?: throw RequestException("settlement id $chequeHandleId is not present in our DB! ")

        val invoiceObj = invoiceSettlementMappingService.getInvoicesForSettlement(settlement.id)
        val settlementInvoiceObj = invoiceSettlementMappingService.getSettlementInvoiceBySettlementId(settlement.id)
        val invoiceAmtMap = settlementInvoiceObj.associateBy { it.id.invoiceId }
        invoiceService.updateInvoiceAmtForBounceCheque(invoiceObj, invoiceAmtMap)
        chequeHandle.status =ChequeHandleType.CANCELLED
        chequeHandlingRepo.save(chequeHandle)
        settlement.reversed = true
        val settlementData = settlementRepo.save(settlement)
        receiptService.handleReceiptReversal(ReceiptReversalDto(settlementData.id, null, userId))
    }


    @Transactional
    fun updateBulkChequeStatus(chequeUpdate: ChequeHandleStatusChangeDto): Result {
        log.info("bulk status change for : ${chequeUpdate.chequeNum} ")
        val chequeHandleDto: ChequeHandle =
            chequeHandlingRepo.getChequeForStatusChange(chequeUpdate.chequeNum?:"",chequeUpdate.pdi,chequeUpdate.receiptId?:"")
                ?: chequeHandlingRepo.getChequeForStatusChangeAdvance(chequeUpdate.chequeNum?:"",chequeUpdate.pdi,chequeUpdate.receiptId?:"")
                ?: throw RequestException("cheque details not found.")
        if(chequeHandleDto.status!!.name.uppercase() == ChequeHandleType.CLEARED.name || chequeHandleDto.status!!.name.uppercase() == ChequeHandleType.BOUNCE.name || chequeHandleDto.status!!.name.uppercase() == ChequeHandleType.CANCELLED.name) throw RequestException("cheque already in ${chequeHandleDto.status!!.name.uppercase()} state.")
        if(chequeUpdate.status == chequeHandleDto.status) throw RequestException("cheque status is already updated.")
        val rows: Int
        val tenant = chequeHandleDto.tenant
        if (chequeUpdate.status!!.name.uppercase() == ChequeHandleType.BOUNCE.name) {
            val checkers = checkerService.findCheckers(tenant,null,null)
            if (checkers.isEmpty()) throw RequestException("checker details not found for tenant $tenant .")
            chequeUpdate.status = ChequeHandleType.PENDING_APPROVAL

            val chargeFlag = (chequeUpdate.chargeAmt ?: 0.0) > 0.0
            val bounceObj = ChequeUpdateDto(chequeHandleDto.id!!, (chequeUpdate.refDate?:LocalDate.now()).atStartOfDay(),chequeUpdate.status!!,"bounced",DefaultConst.DEFAULT_USER,null,chargeFlag,chequeUpdate.chargeAmt)
            val pendingStatusObj =  updateCheckerDetail(bounceObj, tenant,checkers[0],null)
            checkerStatusChange(checkers[0]!!.userId, true, chequeHandleDto.id!!, tenant,null,pendingStatusObj)
        } else if(chequeUpdate.status!!.name.uppercase() == ChequeHandleType.CLEARED.name){

            if (chequeHandleDto.status!!.name != ChequeHandleType.RECEIVED.name)
                throw RequestException("cheque status not in RECEIVED state cant be CLEARED !")
            rows = chequeHandlingRepo.updateChequeStatus(chequeHandleDto.id!!, (chequeUpdate.refDate?:LocalDate.now()).atStartOfDay(), chequeUpdate.status,"", DefaultConst.DEFAULT_USER)
            log.debug("rows updated $rows")
        }
        if(chequeUpdate.status!!.name != ChequeHandleType.RECEIVED.name){
            log.debug("chequeUpdate.status :: ${chequeUpdate.status!!.name}")
            chequeHandlingLog.save(ChequeHandleLog(null, chequeHandleDto.id!!,
                chequeUpdate.status!!, (chequeUpdate.refDate?.atStartOfDay())?: LocalDateTime.now(), DefaultConst.DEFAULT_USER))
        }

        return success
    }


    fun getChequeData(
        page: Int?,
        size: Int?,
        tenant: String,
        ds: String? = null,
        partnerIds: List<Long>?,
        chequeNum: String?,
        settlementNum: String?,
        status: ChequeHandleType?,
        bankName: String?,
        from: LocalDate?,
        to: LocalDate?,
        partnerDetailId: Long?,
        advancePaymentNumber: String?,
        distributorId: Long?
    ): PaginationDto {
        val pagination = PageRequest.of(page ?: 0, size ?: 10)
        val companyMapping = if(distributorId != null) companyService.getTenantByPDI(distributorId) else companyService.getCompanyTenantMappingObject(ds?:tenant)
        val tenants = companyService.findTenants(companyMapping!!.tenant)
        val res: Page<ChequeHandleDto>
        if (partnerIds.isNullOrEmpty()) {
            res = chequeHandlingRepo.getChequeHandlingDataWithoutIdsV2(
                chequeNum,
                status,
                bankName,
                from,
                to,
                tenants,
                partnerDetailId,
                pagination
            )
        } else {
            res = chequeHandlingRepo.getChequeHandlingDataV2(
                chequeNum,
                status,
                bankName,
                partnerIds,
                from,
                to,
                tenants,
                partnerDetailId,
                pagination
            )
        }

        val users = mutableListOf<String>()
        res.content.forEach{
            users.add(it.assignedTo.toString())
        }
        val userMap = if(users.isEmpty()){
            emptyMap()
        }else{
            try {
                userProxy.getUserList(users.distinct())?.associateBy { it.id }
            }catch (e: Exception){
                log.error("Error in user proxy", e)
                emptyMap()
            }
        }

        res.content.forEach{
            it.assignedTo = userMap?.get(it.assignedTo)?.name ?: it.assignedTo
        }

        val chequeTooltips = getChequeStatusTooltip(res.content)
        return PaginationDto(res.totalElements, res.totalPages, res.hasPrevious(), res.hasNext(), chequeTooltips)
    }

    fun validateChequeStatus(id: Long, status: ChequeHandleType): Boolean{
        val cheque = chequeHandlingRepo.get(id)
        if(status == ChequeHandleType.BOUNCE){
            return (cheque?.status == ChequeHandleType.BOUNCE || cheque?.status == ChequeHandleType.AFTER_CLEAR_BOUNCE)
        }
        return (cheque?.status == status)
    }


    fun validateChequeStatusUpdate(currentStatus: ChequeHandleType, newStatus: ChequeHandleType): Boolean {
        val allowedStatuses = validTransitions[currentStatus]
        return allowedStatuses?.contains(newStatus) ?: false
    }

    fun validateNewEntry(chequeNumber: String,pdi:Long,chequeDate:LocalDate,tenant: String): Boolean {
        val existingCheque = chequeHandlingRepo.findDuplicateCheques(
            chequeNumber, pdi, chequeDate, tenant
        )
        return existingCheque == null
    }

    fun getOutstandingPdcAmount(tenant: String, partnerDetailId: Long): BigDecimal {
        return chequeHandlingRepo.getTotalOutstandingChequeAmount(partnerDetailId,tenant)?:BigDecimal.ZERO
    }

    fun getDueInvoiceOutstandingPdcAmount(tenant: String, partnerDetailId: Long): BigDecimal {
        return chequeHandlingRepo.getTotalDueOutstandingChequeAmount(partnerDetailId,tenant)?:BigDecimal.ZERO
    }


}
