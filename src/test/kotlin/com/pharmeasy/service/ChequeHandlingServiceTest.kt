package com.pharmeasy.service

import com.pharmeasy.data.BkInvoice
import com.pharmeasy.data.CheckerDetails
import com.pharmeasy.data.ChequeHandle
import com.pharmeasy.data.ChequeHandleLog
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.InvoiceSettlement
import com.pharmeasy.data.InvoiceSettlementId
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.Settlement
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.AggregatedChequeHandleDto
import com.pharmeasy.model.ChequeHandleDto
import com.pharmeasy.model.ChequeHandleStatusChangeDto
import com.pharmeasy.model.ChequeUpdateDto
import com.pharmeasy.model.CreateChequeHandleDto
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.User
import com.pharmeasy.proxy.UserProxy
import com.pharmeasy.repo.ChequeHandleLogRepo
import com.pharmeasy.repo.ChequeHandlingRepo
import com.pharmeasy.repo.ReceiptRepo
import com.pharmeasy.repo.SettlementRepo
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.CheckerType
import com.pharmeasy.type.ChequeHandleType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ReceiptStatus
import com.pharmeasy.type.Role
import com.pharmeasy.type.Status
import com.pharmeasy.model.Result
import com.pharmeasy.model.success
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.util.ReflectionTestUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

class ChequeHandlingServiceTest {

    companion object {
//        private val success = Result(200, "SUCCESS")
    }

    @MockK(relaxed = true) private lateinit var chequeHandlingRepo: ChequeHandlingRepo
    @MockK(relaxed = true) private lateinit var chequeHandlingLog: ChequeHandleLogRepo
    @MockK(relaxed = true) private lateinit var adjustmentService: AdjustmentService
    @MockK(relaxed = true) private lateinit var settlementRepo: SettlementRepo
    @MockK(relaxed = true) private lateinit var documentMasterService: DocumentMasterService
    @MockK(relaxed = true) private lateinit var checkerService: CheckerService
    @MockK(relaxed = true) private lateinit var companyService: CompanyService
    @MockK(relaxed = true) private lateinit var settlementService: SettlementService
    @MockK(relaxed = true) private lateinit var advancePaymentService: AdvancePaymentService
    @MockK(relaxed = true) private lateinit var s3FileUtilityService : S3FileUtilityService
    @MockK(relaxed = true) private lateinit var receiptService: ReceiptService
    @MockK(relaxed = true) private lateinit var retailerDebitNoteService: RetailerDebitNoteService
    @MockK(relaxed = true) private lateinit var receiptRepo: ReceiptRepo
    @MockK(relaxed = true) private lateinit var userProxy: UserProxy
    @MockK(relaxed = true) private lateinit var retailerDebitNoteSettlementMappingService: RetailerDebitNoteSettlementMappingService
    @MockK(relaxed = true) private lateinit var invoiceService: InvoiceService
    @MockK(relaxed = true) private lateinit var invoiceSettlementMappingService: InvoiceSettlementMappingService

    private lateinit var chequeHandlingService: ChequeHandlingService

    private lateinit var sampleChequeHandle: ChequeHandle
    private lateinit var sampleCheckerDetails: CheckerDetails
    private lateinit var sampleSettlement: Settlement
    private lateinit var sampleReceipt: Receipt
    private lateinit var sampleChequeHandleDto: ChequeHandleDto
    private lateinit var sampleChequeHandleLog: ChequeHandleLog
    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this, relaxUnitFun = true)
        advancePaymentService = mockk(relaxed = true)
        receiptService = mockk(relaxed = true)
        retailerDebitNoteService = mockk(relaxed = true)
        chequeHandlingService = ChequeHandlingService(
            dnToRetailerCBHsn = "test-hsn",
            dnToRetailerCBTax = 18,
            chequeHandlingRepo = chequeHandlingRepo,
            chequeHandlingLog = chequeHandlingLog,
            adjustmentService = adjustmentService,
            documentMasterService = documentMasterService,
            checkerService = checkerService,
            companyService = companyService,
            s3FileUtilityService = s3FileUtilityService,
            userProxy = userProxy,
            retailerDebitNoteSettlementMappingService = retailerDebitNoteSettlementMappingService,
            invoiceSettlementMappingService = invoiceSettlementMappingService
        )
        // Manually inject the field using reflection
        ReflectionTestUtils.setField(chequeHandlingService, "advancePaymentService", advancePaymentService)
        ReflectionTestUtils.setField(chequeHandlingService, "receiptService", receiptService)
        ReflectionTestUtils.setField(chequeHandlingService, "retailerDebitNoteService", retailerDebitNoteService)
        ReflectionTestUtils.setField(chequeHandlingService, "receiptRepo", receiptRepo)
        ReflectionTestUtils.setField(chequeHandlingService, "settlementRepo", settlementRepo)
        ReflectionTestUtils.setField(chequeHandlingService, "settlementService", settlementService)
        ReflectionTestUtils.setField(chequeHandlingService, "invoiceService", invoiceService)

        // Initialize test data
        sampleCheckerDetails = CheckerDetails(
            id = 1L,
            userId = "test_checker",
            userName = "Test Checker",
            companyId = 1L,
            type = CheckerType.CHECKER,
            email = "<EMAIL>",
            role = Role.APPROVER,
            isActive = true,
            priority = 1
        )
        sampleChequeHandleDto = ChequeHandleDto(
            l1 = 1L,
            s1 = "test",
            s2 = "test",
            l2 = 1L,
            s3 = "test",
            d1 = 0.0,
            ld1 = LocalDate.now(),
            ld2 = LocalDateTime.now(),
            s4 = "test",
            ch1 = ChequeHandleType.DEPOSITED,
            s5 = "",
            s6 = "",
            b1 = true,
            l3 = 1L,
            b2 = false,
            d2 = 0.0,
            pt1 = PartnerType.CUSTOMER,
            s7 = ""
        )
        sampleChequeHandle = createSampleChequeHandle()

        sampleSettlement = Settlement(
            id = 1L,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "test_user",
            supplierId = 1L,
            supplierName = "Test Supplier",
            amount = 1000.00,
            paidAmount = 1000.00,
            remarks = "Test settlement",
            settlementNumber = "SET-001",
            invoices = mutableListOf(),
            creditNotes = mutableListOf(),
            paymentType = PaymentType.CHEQUE,
            paymentReference = "CHQ123",
            paymentDate = LocalDate.now(),
            partnerId = 1L,
            partnerDetailId = 1L,
            type = PartnerType.VENDOR,
            tenant = "test_tenant",
            chequeDate = LocalDate.now(),
            bankId = 1L,
            bankName = "Test Bank",
            isBounced = false,
            reversed = false,
            advancePayment = mutableListOf(),
            chargeInvoice = mutableListOf(),
            charge = false,
            receipt = listOf(),
            paymentSource = AdvancePaymentSource.SYSTEM,
            retailerDebitNotes = mutableListOf(),
            uuid = "test-uuid"
        )

        sampleReceipt = Receipt(
            id = 1L,
            receiptNumber = "REC-001",
            createdAt = LocalDateTime.now(),
            createdBy = "test_user",
            updatedAt = LocalDateTime.now(),
            updatedBy = "test_user",
            paymentTransactionId = "CHQ123",
            remarks = "",
            amount = 1000.00,
            status = ReceiptStatus.GENERATED,
            advanceAmount = 0.0,
            source = AdvancePaymentSource.RIO_COLLECTIONS,
            tenant = "test_tenant",
            partnerId = 1L,
            partnerDetailId = 1L,
            unUtilizedAmount = 1000.0
        )

        sampleChequeHandleLog = (ChequeHandleLog(
            id = 1L,
            draftId = 1L,
            status = ChequeHandleType.RECEIVED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        ))
        sampleCompanyTenantMapping = CompanyTenantMapping(
            id = 1L,
            companyId = 1L,
            tenant = "test_tenant",
            "test",
            1L,
            1L
        )

        // Configure common mock behaviors to avoid ClassCastException
        every { chequeHandlingLog.save(any<ChequeHandleLog>()) } returns sampleChequeHandleLog
        every { chequeHandlingRepo.save(any<ChequeHandle>()) } returns sampleChequeHandle
        every { chequeHandlingRepo.findById(any()) } returns Optional.empty()
        every { chequeHandlingRepo.getChequeHandlingDataById(any()) } returns sampleChequeHandleDto
    }

    @Test
    fun `addChequeHandleEntry should create new cheque handle entry`() {
        // Given
        val createChequeHandleDto = CreateChequeHandleDto(
            tenant = "test_tenant",
            chequeNumber = "CHQ123",
            settlementId = 1L,
            advanceId = null,
            paymentDate = LocalDate.now()
        )

        // When
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf(sampleCheckerDetails)
        every { chequeHandlingRepo.save(any()) } returns sampleChequeHandle
        every { chequeHandlingLog.save(any<ChequeHandleLog>()) } returns sampleChequeHandleLog
        chequeHandlingService.addChequeHandleEntry("test_user", createChequeHandleDto)

        // Then
        verify { chequeHandlingRepo.save(any()) }
        verify { chequeHandlingLog.save(any()) }
    }

    @Test
    fun `updateChequeStatus should throw exception when cheque not found`() {
        // Given
        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.DEPOSITED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")
        }
    }

    @Test
    fun `updateChequeStatus should throw exception for invalid status transition`() {
        // Given
        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CLEARED, // Invalid transition from RECEIVED to CLEARED
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")
        }
    }

    @Test
    fun `validateChequeStatus should return true for matching status`() {
        // Given
        every { chequeHandlingRepo.get(any()) } returns sampleChequeHandle

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.RECEIVED)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatus should return true for bounce status variations`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.BOUNCE
        every { chequeHandlingRepo.get(any()) } returns sampleChequeHandle

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.BOUNCE)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatus should return false for non-matching status`() {
        // Given
        every { chequeHandlingRepo.get(any()) } returns sampleChequeHandle

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.CLEARED)

        // Then
        assertFalse(result)
    }

    @Test
    fun `validateChequeStatusUpdate should return true for valid transition`() {
        // Given
        val currentStatus = ChequeHandleType.RECEIVED
        val newStatus = ChequeHandleType.DEPOSITED

        // When
        val result = chequeHandlingService.validateChequeStatusUpdate(currentStatus, newStatus)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatusUpdate should return false for invalid transition`() {
        // Given
        val currentStatus = ChequeHandleType.RECEIVED
        val newStatus = ChequeHandleType.CLEARED

        // When
        val result = chequeHandlingService.validateChequeStatusUpdate(currentStatus, newStatus)

        // Then
        assertFalse(result)
    }

    @Test
    fun `validateNewEntry should return true for new cheque`() {
        // Given
        val testDate = LocalDate.now()
        every { chequeHandlingRepo.findDuplicateCheques("CHQ123", 1L, testDate, "test_tenant") } returns null

        // When
        val result = chequeHandlingService.validateNewEntry(
            "CHQ123",
            1L,
            testDate,
            "test_tenant"
        )

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateNewEntry should return false for duplicate cheque`() {
        // Given
        val testDate = LocalDate.now()
        every { chequeHandlingRepo.findDuplicateCheques("CHQ123", 1L, testDate, "test_tenant") } returns sampleChequeHandle


        // When
        val result = chequeHandlingService.validateNewEntry(
            "CHQ123",
            1L,
            testDate,
            "test_tenant"
        )

        // Then
        assertFalse(result)
    }

    @Test
    fun `getOutstandingPdcAmount should return correct amount`() {
        // Given
        val expectedAmount = BigDecimal("1000.00")
        every { chequeHandlingRepo.getTotalOutstandingChequeAmount(any(), any()) } returns expectedAmount

        // When
        val result = chequeHandlingService.getOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(expectedAmount, result)
    }

    @Test
    fun `getOutstandingPdcAmount should return zero when no amount found`() {
        // Given
        every { chequeHandlingRepo.getTotalOutstandingChequeAmount(any(), any()) } returns null

        // When
        val result = chequeHandlingService.getOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getDueInvoiceOutstandingPdcAmount should return correct amount`() {
        // Given
        val expectedAmount = BigDecimal("500.00")
        every { chequeHandlingRepo.getTotalDueOutstandingChequeAmount(any(), any()) } returns expectedAmount

        // When
        val result = chequeHandlingService.getDueInvoiceOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(expectedAmount, result)
    }

    @Test
    fun `getDueInvoiceOutstandingPdcAmount should return zero when no amount found`() {
        // Given
        every { chequeHandlingRepo.getTotalDueOutstandingChequeAmount(any(), any()) } returns null

        // When
        val result = chequeHandlingService.getDueInvoiceOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getChequeStatusTooltip should return empty list for empty input`() {
        // Given
        val emptyCheques = emptyList<ChequeHandleDto>()

        // When
        val result = chequeHandlingService.getChequeStatusTooltip(emptyCheques)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getChequeStatusTooltip should return correct tooltip for cheque with status logs`() {
        // Given
        var chequeDto = sampleChequeHandleDto
        chequeDto.status = ChequeHandleType.RECEIVED
        chequeDto.bounceDate = LocalDateTime.now()
        chequeDto.chequeNum = "CHQ123"
        chequeDto.id = 1L

        val statusLog = ChequeHandleLog(
            id = 1L,
            draftId = 1L,
            status = ChequeHandleType.RECEIVED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        every { chequeHandlingLog.getByDraftIds(any()) } returns listOf(statusLog)

        // When
        val result = chequeHandlingService.getChequeStatusTooltip(listOf(chequeDto))

        // Then
        assertEquals(1, result.size)
        assertNotNull(result[0].recievedOn)
    }

    @Test
    fun `getTotalBounceNumber should return correct count`() {
        // Given
        val expectedCount = 5L
        every { companyService.findTenants("test_tenant") } returns mutableListOf("test_tenant")
        every { chequeHandlingRepo.getPastBounceNumberV2(any(), any()) } returns expectedCount

        // When
        val result = chequeHandlingService.getTotalBounceNumber("test_tenant", 1L)

        // Then
        assertEquals(expectedCount, result)
    }

    @Test
    fun `updateChequeStatus should handle cleared status with advance payment`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.DEPOSITED
        sampleChequeHandle.advancePaymentId = 1L
        every { chequeHandlingRepo.findById(any()) } returns Optional.of(sampleChequeHandle)
        every { chequeHandlingRepo.getChequeHandlingDataById(any()) } returns sampleChequeHandleDto
        every { chequeHandlingLog.getByDraftIdAndStatus(1L, ChequeHandleType.CLEARED) } returns emptyList()
        every { settlementRepo.findById(any()) } returns Optional.of(sampleSettlement)
        every { receiptService.checkExistingReceipt(sampleSettlement) } returns sampleReceipt

        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CLEARED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CLEARED, result?.status)
        assertTrue(result?.settled == true)
        verify { advancePaymentService.changeAdvancePaymentStatus(1L, Status.APPROVED) }
    }

    @Test
    fun `updateChequeStatus should handle cleared status with RIO collections receipt`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.DEPOSITED
        sampleReceipt.source = AdvancePaymentSource.RIO_COLLECTIONS
        sampleReceipt.advanceAmount = 100.0
        every { chequeHandlingRepo.findById(any()) } returns Optional.of(sampleChequeHandle)
        every { chequeHandlingRepo.getChequeHandlingDataById(any()) } returns sampleChequeHandleDto
        every { chequeHandlingLog.getByDraftIdAndStatus(1L, ChequeHandleType.CLEARED) } returns emptyList()
        every { settlementRepo.findById(any()) } returns Optional.of(sampleSettlement)
        every { receiptService.checkExistingReceipt(sampleSettlement) } returns sampleReceipt

        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CLEARED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CLEARED, result?.status)
        assertTrue(result?.settled == true)
    }

    @Test
    fun `updateChequeStatus should handle cancelled status with settlement`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.RECEIVED
        sampleChequeHandle.settlementId = 1L
        every { chequeHandlingRepo.findById(any()) } returns Optional.of(sampleChequeHandle)
        every { settlementRepo.findById(any()) } returns Optional.of(sampleSettlement)
        every { settlementRepo.save(sampleSettlement) } returns sampleSettlement
        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CANCELLED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CANCELLED, result?.status)
        verify { settlementRepo.save(sampleSettlement) }
    }

    @Test
    fun `updateChequeStatus should handle cancelled status with advance payment`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.RECEIVED
        sampleChequeHandle.advancePaymentId = 1L
        every { chequeHandlingRepo.findById(any()) } returns Optional.of(sampleChequeHandle)
        every { settlementRepo.findById(any()) } returns Optional.of(sampleSettlement)
        every { settlementRepo.save(sampleSettlement) } returns sampleSettlement

        val chequeUpdate = ChequeUpdateDto(
            id = 1L,
            status = ChequeHandleType.CANCELLED,
            updatedBy = "test_user",
            updatedOn = LocalDateTime.now(),
            reason = "",
            assignTo = "test_checker",
            charge = false,
            chargeAmt = 0.0,
            bounceDate = LocalDateTime.now()
        )

        // When
        val result = chequeHandlingService.updateChequeStatus(chequeUpdate, "test_tenant", "<EMAIL>")

        // Then
        assertEquals(ChequeHandleType.CANCELLED, result?.status)
    }

    @Test
    fun `updateBouncedStatus should handle company not found`() {
        // Given
        every { companyService.getCompanyByTenant(any()) } returns null

        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.updateBouncedStatus("test_user", true, 1L, "test_tenant", sampleCheckerDetails)
        }
    }

    @Test
    fun `updateBulkChequeStatus should handle cleared status`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.RECEIVED
        every { chequeHandlingRepo.getChequeForStatusChange(any(), any(), any()) } returns sampleChequeHandle
        every { chequeHandlingRepo.updateChequeStatus(any(), any(), any(), any(), any()) } returns 1

        val chequeUpdate = ChequeHandleStatusChangeDto(
            chequeNum = "CHQ123",
            pdi = 1L,
            status = ChequeHandleType.CLEARED,
            refDate = LocalDate.now(),
            chargeAmt = 0.0
        )

        // When
        val result = chequeHandlingService.updateBulkChequeStatus(chequeUpdate)

        // Then
        assertEquals("success", result.message)
        verify { chequeHandlingLog.save(any()) }
    }

    @Test
    fun `validateChequeStatus should handle bounce status`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.BOUNCE
        every { chequeHandlingRepo.get(any()) } returns sampleChequeHandle

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.BOUNCE)

        // Then
        assertTrue(result)
    }

    @Test
    fun `validateChequeStatus should handle after clear bounce status`() {
        // Given
        sampleChequeHandle.status = ChequeHandleType.AFTER_CLEAR_BOUNCE
        every { chequeHandlingRepo.get(any()) } returns sampleChequeHandle

        // When
        val result = chequeHandlingService.validateChequeStatus(1L, ChequeHandleType.BOUNCE)

        // Then
        assertTrue(result)
    }

    @Test
    fun `getOutstandingPdcAmount should return zero for no amount`() {
        // Given
        every { chequeHandlingRepo.getTotalOutstandingChequeAmount(any(), any()) } returns null

        // When
        val result = chequeHandlingService.getOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getDueInvoiceOutstandingPdcAmount should return zero for no amount`() {
        // Given
        every { chequeHandlingRepo.getTotalDueOutstandingChequeAmount(any(), any()) } returns null

        // When
        val result = chequeHandlingService.getDueInvoiceOutstandingPdcAmount("test_tenant", 1L)

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getChequeStatusTooltip should return empty list for empty cheques`() {
        // When
        val result = chequeHandlingService.getChequeStatusTooltip(emptyList())

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getChequeStatusTooltip should return tooltips with all status dates`() {
        // Given
        val chequeDto = ChequeHandleDto(
            l1 = 1L,
            s1 = "test",
            s2 = "test",
            l2 = 1L,
            s3 = "test",
            d1 = 0.0,
            ld1 = LocalDate.now(),
            ld2 = LocalDateTime.now(),
            s4 = "test",
            ch1 = ChequeHandleType.RECEIVED,
            s5 = "",
            s6 = "",
            b1 = true,
            l3 = 1L,
            b2 = false,
            d2 = 0.0,
            pt1 = PartnerType.CUSTOMER,
            s7 = ""
        )
        val receivedLog = ChequeHandleLog(
            id = 1L,
            draftId = 1L,
            status = ChequeHandleType.RECEIVED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        val depositedLog = ChequeHandleLog(
            id = 2L,
            draftId = 1L,
            status = ChequeHandleType.DEPOSITED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        val clearedLog = ChequeHandleLog(
            id = 3L,
            draftId = 1L,
            status = ChequeHandleType.CLEARED,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user"
        )
        every { chequeHandlingLog.getByDraftIds(listOf(1L)) } returns listOf(receivedLog, depositedLog, clearedLog)

        // When
        val result = chequeHandlingService.getChequeStatusTooltip(listOf(chequeDto))

        // Then
        assertEquals(1, result.size)
        assertNotNull(result[0].recievedOn)
        assertNotNull(result[0].depositedOn)
        assertNotNull(result[0].clearedOn)
    }

    @Test
    fun `getAggregatedChequeData should return data for tenant`() {
        // Given
        val expectedData = AggregatedChequeHandleDto(
            totalRecievedCheques = 10L,
            totalRecievedChequesAmount = BigDecimal("10000.00"),
            totalDepositedCheques = 5L,
            totalDepositedChequesAmount = BigDecimal("5000.00")
        )
        every { companyService.findTenants("test_tenant") } returns mutableListOf("test_tenant")
        every { chequeHandlingRepo.getAggregatedChequeDataV2(mutableListOf("test_tenant"), 1L) } returns expectedData

        // When
        val result = chequeHandlingService.getAggregatedChequeData("test_tenant", null, 1L)

        // Then
        assertEquals(expectedData, result)
    }

    @Test
    fun `getChequeUrl should throw exception when tenant not found`() {
        // Given
        every { companyService.findTenants("test_tenant") } returns mutableListOf()

        // When/Then
        assertThrows(RequestException::class.java) {
            chequeHandlingService.getChequeUrl("test_tenant", null, LocalDate.now(), LocalDate.now(), ChequeHandleType.RECEIVED)
        }
    }

    @Test
    fun `should throw RequestException if user is a checker`() {
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf(sampleChecker("user1"))

        assertThrows<RequestException> {
            chequeHandlingService.checkerCheck("user1", "tenant1")
        }
    }

    @Test
    fun `should return checker if user is not a checker`() {
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf(sampleChecker("user2"))

        val result = chequeHandlingService.checkerCheck("user1", "tenant1")
        assertNotNull(result)
        assertEquals("user2", result?.userId)
    }

    @Test
    fun `should return null if no checkers found`() {
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf()

        val result = chequeHandlingService.checkerCheck("user1", "tenant1")
        assertNull(result)
    }

    @Test
    fun `should update cheque checker successfully`() {
        sampleChequeHandle.status= ChequeHandleType.PENDING_APPROVAL
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf(sampleChecker("user1"))
        every { chequeHandlingRepo.get(1L) } returns sampleChequeHandle
        every { companyService.getCompanyTenantMappingObject(sampleChequeHandle.tenant) } returns sampleCompanyTenantMapping
        every { checkerService.getCheckerByCompanyId(1L) } returns mutableListOf(sampleChecker("user1"))
        every { chequeHandlingRepo.save(sampleChequeHandle) } returns sampleChequeHandle

        val result = chequeHandlingService.updateChequeChecker(1L, 1L, sampleChequeHandle.tenant, "user1")

        assertEquals(success, result)
        verify { chequeHandlingRepo.save(any()) }
    }

    @Test
    fun `should throw if no checker found`() {
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf()
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if cheque not found`() {
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf()
        every { chequeHandlingRepo.get(1L) } returns null
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if cheque not in PENDING_APPROVAL`() {
        val cheque = sampleChequeHandle.copy(status = ChequeHandleType.RECEIVED)
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf(mockk(relaxed = true))
        every { chequeHandlingRepo.get(1L) } returns cheque
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if company not found`() {
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf(mockk(relaxed = true))
        every { chequeHandlingRepo.get(1L) } returns sampleChequeHandle
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns null
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "checker1")
        }
    }

    @Test
    fun `should throw if user is not creator or checker`() {
        every { checkerService.findCheckers(any(), any(), any()) } returns mutableListOf()
        every { chequeHandlingRepo.get(1L) } returns sampleChequeHandle
        every { companyService.getCompanyTenantMappingObject("test_tenant") } returns sampleCompanyTenantMapping
        every { checkerService.getCheckerByCompanyId(1L) } returns mutableListOf()
        assertThrows<RequestException> {
            chequeHandlingService.updateChequeChecker(1L, 2L, "tenant1", "notallowed")
        }
    }

    @Test
    fun `should throw RequestException when tenant not found`() {
        every { companyService.findTenants("test_tenant") } returns mutableListOf()

        assertThrows<RequestException> {
            chequeHandlingService.getChequeUrl("tenant1", null, LocalDate.now(), LocalDate.now(), ChequeHandleType.RECEIVED)
        }
    }

    @Test
    fun `should reverse settlement and invoice successfully`() {
        val invoiceList = listOf(mockk<BkInvoice>(relaxed = true))
        val settlementInvoiceList = listOf(
            InvoiceSettlement(
                id = InvoiceSettlementId(1L, 1L),
                amount = BigDecimal("100.00"),
                paidAmount = BigDecimal("100.00"),
                invoiceStatus = InvoiceStatus.PAID
            )
        )

        every { chequeHandlingRepo.findById(1L) } returns Optional.of(sampleChequeHandle)
        every { settlementRepo.findById(1L) } returns Optional.of(sampleSettlement)
        every { invoiceSettlementMappingService.getInvoicesForSettlement(1L) } returns invoiceList
        every { invoiceSettlementMappingService.getSettlementInvoiceBySettlementId(1L) } returns settlementInvoiceList
        every { settlementRepo.save(sampleSettlement) } returns sampleSettlement

        chequeHandlingService.reverseSettlementAndInvoice("user1", 1L)

        verify { invoiceService.updateInvoiceAmtForBounceCheque(eq(invoiceList), any()) }
        verify {
            chequeHandlingRepo.save(match {
                it.status == ChequeHandleType.CANCELLED
            })
        }
    }

    @Test
    fun `getChequeData should return pagination dto with user names`() {
        // Given
        val page = 0
        val size = 10
        val tenant = "test_tenant"
        val ds: String? = null
        val partnerIds: List<Long>? = null
        val chequeNum = "CHQ123"
        val settlementNum = "SETT123"
        val status = ChequeHandleType.DEPOSITED
        val bankName = "Test Bank"
        val from = LocalDate.now().minusDays(7)
        val to = LocalDate.now()
        val partnerDetailId = 1L
        val advancePaymentNumber = "ADV123"
        val distributorId: Long? = null

        val pagination = PageRequest.of(page, size)
        val tenantList: MutableList<String?> = mutableListOf("test_tenant")
        val pageData = PageImpl(listOf(sampleChequeHandleDto), pagination, 1)
        val userDto = User("1L", "test_checker", "Test Checker", "")

        every { userProxy.getUserList(listOf("test_checker")) } returns listOf(userDto)
        every { companyService.getCompanyTenantMappingObject(tenant) } returns sampleCompanyTenantMapping
        every { companyService.findTenants(tenant) } returns tenantList
        every { chequeHandlingRepo.getChequeHandlingDataWithoutIdsV2(
            chequeNum,
            status,
            bankName,
            from,
            to,
            tenantList,
            partnerDetailId,
            pagination
        ) } returns pageData
        every { userProxy.getUserList(listOf("test_checker")) } returns listOf(userDto)

        // When
        val result = chequeHandlingService.getChequeData(
            page,
            size,
            tenant,
            ds,
            partnerIds,
            chequeNum,
            settlementNum,
            status,
            bankName,
            from,
            to,
            partnerDetailId,
            advancePaymentNumber,
            distributorId
        )

        // Then
        assertEquals(1, result.elements)
        assertFalse(result.hasPrevious == true)
        assertFalse(result.hasNext == true)
    }

    @Test
    fun `updateBulkChequeStatus should throw if status is same as current`() {
        // Given
        val chequeUpdateDto = ChequeHandleStatusChangeDto(
            chequeNum = "CHQ123",
            pdi = 1L,
            receiptId = "REC-001",
            status = ChequeHandleType.RECEIVED
        )
        val chequeHandle = sampleChequeHandle.copy(status = ChequeHandleType.RECEIVED)

        every { chequeHandlingRepo.getChequeForStatusChange(any(), any(), any()) } returns chequeHandle

        // Then
        assertThrows<RequestException> {
            chequeHandlingService.updateBulkChequeStatus(chequeUpdateDto)
        }
    }

    @Test
    fun `updateBulkChequeStatus should throw if trying to clear non-RECEIVED cheque`() {
        // Given
        val chequeUpdateDto = ChequeHandleStatusChangeDto(
            chequeNum = "CHQ123",
            pdi = 1L,
            receiptId = "REC-001",
            status = ChequeHandleType.CLEARED
        )
        val chequeHandle = sampleChequeHandle.copy(status = ChequeHandleType.BOUNCE)

        every { chequeHandlingRepo.getChequeForStatusChange(any(), any(), any()) } returns chequeHandle

        // Then
        assertThrows<RequestException> {
            chequeHandlingService.updateBulkChequeStatus(chequeUpdateDto)
        }
    }

    private fun sampleChecker(userId: String = "user1") = CheckerDetails(
        id = 1L,
        companyId = 1L,
        userName = "Test User",
        userId = userId,
        type = CheckerType.CHECKER,
        email = "<EMAIL>",
        role = Role.APPROVER,
        isActive = true,
        priority = 0
    )

    private fun createSampleChequeHandle(): ChequeHandle {
        return ChequeHandle(
            tenant = "test_tenant",
            chequeNum = "CHQ123",
            settlementId = 1L,
            assignTo = "test_checker",
            status = ChequeHandleType.RECEIVED,
            inApprove = false,
            settled = false,
            advancePaymentId = null
        ).apply {
            id = 1L
            createdBy = "test_user"
            updatedBy = "test_user"
            createdOn = LocalDateTime.now()
            updatedOn = LocalDateTime.now()
        }
    }
}
